"""
Enhanced Database Connection Manager for Phase 2 Memory Leak Resolution.

This module provides comprehensive database connection management with:
- Connection timeout and retry logic
- Connection pool monitoring
- Connection leak detection
- Automatic cleanup and recovery
"""

import logging
import asyncio
import time
import weakref
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from sqlalchemy import create_engine, event, Engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import Pool
from sqlalchemy.exc import DisconnectionError, TimeoutError, OperationalError
import gc

from ..settings.database import DatabaseConfig
from ..monitoring.metrics import metrics

logger = logging.getLogger(__name__)


@dataclass
class ConnectionInfo:
    """Information about a database connection."""
    connection_id: str
    created_at: datetime
    last_used: datetime
    query_count: int = 0
    is_active: bool = True
    stack_trace: Optional[str] = None


@dataclass
class ConnectionPoolStats:
    """Statistics about the connection pool."""
    total_connections: int = 0
    active_connections: int = 0
    idle_connections: int = 0
    checked_out_connections: int = 0
    overflow_connections: int = 0
    invalid_connections: int = 0
    pool_size: int = 0
    max_overflow: int = 0
    connection_leaks: int = 0
    avg_checkout_time: float = 0.0
    total_checkouts: int = 0
    failed_checkouts: int = 0


class EnhancedConnectionManager:
    """
    Enhanced database connection manager with comprehensive monitoring,
    leak detection, and automatic recovery capabilities.
    """
    
    def __init__(self, database_config: DatabaseConfig):
        """Initialize the enhanced connection manager."""
        self.config = database_config
        self.logger = logging.getLogger(__name__)
        
        # Connection tracking
        self._active_connections: Dict[str, ConnectionInfo] = {}
        self._connection_weak_refs: Dict[str, weakref.ref] = {}
        self._checkout_times: Dict[str, float] = {}
        
        # Pool monitoring
        self._pool_stats = ConnectionPoolStats()
        self._last_stats_update = datetime.now()
        
        # Leak detection
        self._leak_detection_enabled = True
        self._max_connection_age = timedelta(hours=1)
        self._max_idle_time = timedelta(minutes=30)
        self._leak_threshold = 50  # Number of connections before considering it a leak
        
        # Background tasks
        self._background_tasks: List[asyncio.Task] = []
        self._shutdown_event = asyncio.Event()
        self._is_shutting_down = False
        
        # Create enhanced engine
        self.engine = self._create_enhanced_engine()
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # Start monitoring
        self._start_background_monitoring()
        
        self.logger.info("Enhanced connection manager initialized")
    
    def _create_enhanced_engine(self) -> Engine:
        """Create database engine with enhanced monitoring and error handling."""
        # Enhanced connection pool configuration
        pool_config = {
            "pool_size": self.config.pool_size,
            "max_overflow": self.config.max_overflow,
            "pool_pre_ping": self.config.pool_pre_ping,
            "pool_recycle": self.config.pool_recycle,
            "pool_timeout": 30,  # Timeout for getting connection from pool
            "pool_reset_on_return": "commit",
            "pool_logging_name": "datagenius_enhanced_pool",
        }
        
        # Create engine with enhanced configuration
        engine = create_engine(
            self.config.url,
            echo=self.config.echo,
            connect_args=self.config.connect_args,
            **pool_config
        )
        
        # Register event listeners for monitoring
        self._register_event_listeners(engine)
        
        return engine
    
    def _register_event_listeners(self, engine: Engine):
        """Register SQLAlchemy event listeners for monitoring."""
        
        @event.listens_for(engine, "connect")
        def on_connect(dbapi_connection, connection_record):
            """Handle new database connections."""
            connection_id = str(id(dbapi_connection))
            
            # Track connection
            self._active_connections[connection_id] = ConnectionInfo(
                connection_id=connection_id,
                created_at=datetime.now(),
                last_used=datetime.now()
            )
            
            # Create weak reference for leak detection
            def cleanup_callback(ref):
                if connection_id in self._connection_weak_refs:
                    del self._connection_weak_refs[connection_id]
                self.logger.debug(f"Connection {connection_id} weak reference cleaned up")
            
            self._connection_weak_refs[connection_id] = weakref.ref(
                dbapi_connection, cleanup_callback
            )
            
            self.logger.debug(f"New database connection created: {connection_id}")
        
        @event.listens_for(engine, "checkout")
        def on_checkout(dbapi_connection, connection_record, connection_proxy):
            """Handle connection checkout from pool."""
            connection_id = str(id(dbapi_connection))
            self._checkout_times[connection_id] = time.time()
            
            if connection_id in self._active_connections:
                self._active_connections[connection_id].last_used = datetime.now()
                self._active_connections[connection_id].is_active = True
            
            self._pool_stats.total_checkouts += 1
        
        @event.listens_for(engine, "checkin")
        def on_checkin(dbapi_connection, connection_record):
            """Handle connection checkin to pool."""
            connection_id = str(id(dbapi_connection))
            
            # Calculate checkout time
            if connection_id in self._checkout_times:
                checkout_time = time.time() - self._checkout_times[connection_id]
                self._update_avg_checkout_time(checkout_time)
                del self._checkout_times[connection_id]
            
            if connection_id in self._active_connections:
                self._active_connections[connection_id].is_active = False
        
        @event.listens_for(engine, "close")
        def on_close(dbapi_connection, connection_record):
            """Handle connection close."""
            connection_id = str(id(dbapi_connection))
            
            # Clean up tracking
            if connection_id in self._active_connections:
                del self._active_connections[connection_id]
            if connection_id in self._connection_weak_refs:
                del self._connection_weak_refs[connection_id]
            if connection_id in self._checkout_times:
                del self._checkout_times[connection_id]
            
            self.logger.debug(f"Database connection closed: {connection_id}")
        
        @event.listens_for(engine, "close_detached")
        def on_close_detached(dbapi_connection):
            """Handle detached connection close."""
            connection_id = str(id(dbapi_connection))
            self.logger.debug(f"Detached connection closed: {connection_id}")
    
    def _update_avg_checkout_time(self, checkout_time: float):
        """Update average checkout time statistics."""
        current_avg = self._pool_stats.avg_checkout_time
        total_checkouts = self._pool_stats.total_checkouts
        
        if total_checkouts > 0:
            self._pool_stats.avg_checkout_time = (
                (current_avg * (total_checkouts - 1) + checkout_time) / total_checkouts
            )
        else:
            self._pool_stats.avg_checkout_time = checkout_time
    
    def _start_background_monitoring(self):
        """Start background monitoring tasks."""
        try:
            # Connection leak detection task
            task = asyncio.create_task(self._connection_leak_detection_task())
            self._background_tasks.append(task)
            
            # Pool statistics monitoring task
            task = asyncio.create_task(self._pool_monitoring_task())
            self._background_tasks.append(task)
            
            # Connection cleanup task
            task = asyncio.create_task(self._connection_cleanup_task())
            self._background_tasks.append(task)
            
            self.logger.info(f"Started {len(self._background_tasks)} database monitoring tasks")
            
        except Exception as e:
            self.logger.error(f"Failed to start background monitoring: {e}")
            raise

    async def _connection_leak_detection_task(self):
        """Background task to detect and handle connection leaks."""
        self.logger.info("Starting connection leak detection task")

        while not self._is_shutting_down:
            try:
                try:
                    await asyncio.wait_for(
                        self._shutdown_event.wait(),
                        timeout=60  # Check every minute
                    )
                    break
                except asyncio.TimeoutError:
                    pass

                if not self._is_shutting_down:
                    await self._detect_connection_leaks()

            except asyncio.CancelledError:
                self.logger.info("Connection leak detection task cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in connection leak detection: {e}")

        self.logger.info("Connection leak detection task finished")

    async def _pool_monitoring_task(self):
        """Background task to monitor pool statistics."""
        self.logger.info("Starting pool monitoring task")

        while not self._is_shutting_down:
            try:
                try:
                    await asyncio.wait_for(
                        self._shutdown_event.wait(),
                        timeout=300  # Check every 5 minutes
                    )
                    break
                except asyncio.TimeoutError:
                    pass

                if not self._is_shutting_down:
                    await self._update_pool_statistics()

            except asyncio.CancelledError:
                self.logger.info("Pool monitoring task cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in pool monitoring: {e}")

        self.logger.info("Pool monitoring task finished")

    async def _connection_cleanup_task(self):
        """Background task to clean up old and orphaned connections."""
        self.logger.info("Starting connection cleanup task")

        while not self._is_shutting_down:
            try:
                try:
                    await asyncio.wait_for(
                        self._shutdown_event.wait(),
                        timeout=600  # Check every 10 minutes
                    )
                    break
                except asyncio.TimeoutError:
                    pass

                if not self._is_shutting_down:
                    await self._cleanup_old_connections()
                    await self._cleanup_orphaned_weak_references()

            except asyncio.CancelledError:
                self.logger.info("Connection cleanup task cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in connection cleanup: {e}")

        self.logger.info("Connection cleanup task finished")

    async def _detect_connection_leaks(self):
        """Detect potential connection leaks."""
        try:
            current_time = datetime.now()
            potential_leaks = []

            for conn_id, conn_info in self._active_connections.items():
                # Check for old connections
                age = current_time - conn_info.created_at
                if age > self._max_connection_age:
                    potential_leaks.append((conn_id, conn_info, "old_connection"))

                # Check for long-idle connections
                idle_time = current_time - conn_info.last_used
                if idle_time > self._max_idle_time and conn_info.is_active:
                    potential_leaks.append((conn_id, conn_info, "idle_connection"))

            # Check total connection count
            total_connections = len(self._active_connections)
            if total_connections > self._leak_threshold:
                self.logger.warning(
                    f"High connection count detected: {total_connections} connections "
                    f"(threshold: {self._leak_threshold})"
                )
                self._pool_stats.connection_leaks += 1

            # Handle potential leaks
            for conn_id, conn_info, leak_type in potential_leaks:
                self.logger.warning(
                    f"Potential connection leak detected: {conn_id} "
                    f"({leak_type}, age: {current_time - conn_info.created_at})"
                )

                # Update metrics
                metrics.record_database_operation("connection_leak_detected", 1.0)

            if potential_leaks:
                self._pool_stats.connection_leaks += len(potential_leaks)

        except Exception as e:
            self.logger.error(f"Error detecting connection leaks: {e}")

    async def _update_pool_statistics(self):
        """Update connection pool statistics."""
        try:
            pool = self.engine.pool

            # Update pool statistics
            self._pool_stats.pool_size = pool.size()
            self._pool_stats.checked_out_connections = pool.checkedout()
            self._pool_stats.overflow_connections = pool.overflow()
            self._pool_stats.invalid_connections = pool.invalidated()
            self._pool_stats.total_connections = len(self._active_connections)
            self._pool_stats.active_connections = len([
                conn for conn in self._active_connections.values() if conn.is_active
            ])
            self._pool_stats.idle_connections = (
                self._pool_stats.total_connections - self._pool_stats.active_connections
            )

            # Update metrics
            metrics.update_database_connections(self._pool_stats.active_connections)

            # Log statistics periodically
            if (datetime.now() - self._last_stats_update).total_seconds() > 300:  # Every 5 minutes
                self.logger.info(
                    f"Pool stats - Total: {self._pool_stats.total_connections}, "
                    f"Active: {self._pool_stats.active_connections}, "
                    f"Idle: {self._pool_stats.idle_connections}, "
                    f"Checked out: {self._pool_stats.checked_out_connections}, "
                    f"Avg checkout time: {self._pool_stats.avg_checkout_time:.3f}s"
                )
                self._last_stats_update = datetime.now()

        except Exception as e:
            self.logger.error(f"Error updating pool statistics: {e}")

    async def _cleanup_old_connections(self):
        """Clean up old and stale connections."""
        try:
            current_time = datetime.now()
            connections_to_remove = []

            for conn_id, conn_info in self._active_connections.items():
                # Remove very old connections
                age = current_time - conn_info.created_at
                if age > timedelta(hours=2):  # 2 hours max age
                    connections_to_remove.append(conn_id)

            for conn_id in connections_to_remove:
                if conn_id in self._active_connections:
                    del self._active_connections[conn_id]
                if conn_id in self._connection_weak_refs:
                    del self._connection_weak_refs[conn_id]
                if conn_id in self._checkout_times:
                    del self._checkout_times[conn_id]

            if connections_to_remove:
                self.logger.info(f"Cleaned up {len(connections_to_remove)} old connections")

        except Exception as e:
            self.logger.error(f"Error cleaning up old connections: {e}")

    async def _cleanup_orphaned_weak_references(self):
        """Clean up orphaned weak references."""
        try:
            orphaned_refs = []

            for conn_id, weak_ref in list(self._connection_weak_refs.items()):
                if weak_ref() is None:  # Object has been garbage collected
                    orphaned_refs.append(conn_id)

            for conn_id in orphaned_refs:
                if conn_id in self._connection_weak_refs:
                    del self._connection_weak_refs[conn_id]
                if conn_id in self._active_connections:
                    del self._active_connections[conn_id]
                if conn_id in self._checkout_times:
                    del self._checkout_times[conn_id]

            if orphaned_refs:
                self.logger.debug(f"Cleaned up {len(orphaned_refs)} orphaned weak references")

            # Force garbage collection
            gc.collect()

        except Exception as e:
            self.logger.error(f"Error cleaning up orphaned weak references: {e}")

    @asynccontextmanager
    async def get_session_with_retry(self, max_retries: int = 3, retry_delay: float = 1.0):
        """
        Get database session with automatic retry logic.

        Args:
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds
        """
        session = None
        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                session = self.SessionLocal()
                yield session
                session.commit()
                return

            except (DisconnectionError, TimeoutError, OperationalError) as e:
                last_exception = e

                if session:
                    session.rollback()
                    session.close()
                    session = None

                if attempt < max_retries:
                    self.logger.warning(
                        f"Database operation failed (attempt {attempt + 1}/{max_retries + 1}): {e}. "
                        f"Retrying in {retry_delay}s..."
                    )
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    self.logger.error(f"All {max_retries + 1} database attempts failed: {e}")
                    self._pool_stats.failed_checkouts += 1

            except Exception as e:
                if session:
                    session.rollback()
                    session.close()
                raise e

        # If we get here, all retries failed
        if last_exception:
            raise last_exception

    def get_session(self) -> Session:
        """Get a database session (traditional method)."""
        return self.SessionLocal()

    def get_pool_statistics(self) -> ConnectionPoolStats:
        """Get current connection pool statistics."""
        return self._pool_stats

    def get_connection_info(self) -> Dict[str, Any]:
        """Get detailed connection information."""
        current_time = datetime.now()

        connection_details = []
        for conn_id, conn_info in self._active_connections.items():
            age = current_time - conn_info.created_at
            idle_time = current_time - conn_info.last_used

            connection_details.append({
                'connection_id': conn_id,
                'age_seconds': age.total_seconds(),
                'idle_seconds': idle_time.total_seconds(),
                'query_count': conn_info.query_count,
                'is_active': conn_info.is_active,
                'created_at': conn_info.created_at.isoformat(),
                'last_used': conn_info.last_used.isoformat()
            })

        return {
            'total_connections': len(self._active_connections),
            'active_connections': len([c for c in self._active_connections.values() if c.is_active]),
            'connection_details': connection_details,
            'pool_stats': {
                'pool_size': self._pool_stats.pool_size,
                'checked_out': self._pool_stats.checked_out_connections,
                'overflow': self._pool_stats.overflow_connections,
                'invalid': self._pool_stats.invalid_connections,
                'avg_checkout_time': self._pool_stats.avg_checkout_time,
                'total_checkouts': self._pool_stats.total_checkouts,
                'failed_checkouts': self._pool_stats.failed_checkouts,
                'connection_leaks': self._pool_stats.connection_leaks
            }
        }

    async def force_cleanup_connections(self) -> Dict[str, int]:
        """Force cleanup of all connections and return cleanup statistics."""
        cleanup_stats = {
            'connections_closed': 0,
            'weak_refs_cleaned': 0,
            'checkout_times_cleared': 0
        }

        try:
            # Close all tracked connections
            connections_to_close = list(self._active_connections.keys())
            for conn_id in connections_to_close:
                if conn_id in self._active_connections:
                    del self._active_connections[conn_id]
                    cleanup_stats['connections_closed'] += 1

            # Clean up weak references
            weak_refs_to_clean = list(self._connection_weak_refs.keys())
            for conn_id in weak_refs_to_clean:
                if conn_id in self._connection_weak_refs:
                    del self._connection_weak_refs[conn_id]
                    cleanup_stats['weak_refs_cleaned'] += 1

            # Clear checkout times
            checkout_times_to_clear = list(self._checkout_times.keys())
            for conn_id in checkout_times_to_clear:
                if conn_id in self._checkout_times:
                    del self._checkout_times[conn_id]
                    cleanup_stats['checkout_times_cleared'] += 1

            # Force garbage collection
            gc.collect()

            self.logger.info(f"Force cleanup completed: {cleanup_stats}")

        except Exception as e:
            self.logger.error(f"Error in force cleanup: {e}")

        return cleanup_stats

    async def shutdown(self, timeout: float = 30.0) -> None:
        """
        Gracefully shutdown the connection manager.

        Args:
            timeout: Maximum time to wait for tasks to complete
        """
        self.logger.info("Starting connection manager shutdown...")

        try:
            # Set shutdown flag
            self._is_shutting_down = True
            self._shutdown_event.set()

            # Wait for background tasks to complete
            if self._background_tasks:
                self.logger.info(f"Waiting for {len(self._background_tasks)} monitoring tasks to complete...")

                try:
                    await asyncio.wait_for(
                        asyncio.gather(*self._background_tasks, return_exceptions=True),
                        timeout=timeout
                    )
                    self.logger.info("All monitoring tasks completed gracefully")
                except asyncio.TimeoutError:
                    self.logger.warning(f"Monitoring tasks did not complete within {timeout}s, cancelling...")

                    # Cancel remaining tasks
                    for task in self._background_tasks:
                        if not task.done():
                            task.cancel()

                    # Wait a bit more for cancellation
                    try:
                        await asyncio.wait_for(
                            asyncio.gather(*self._background_tasks, return_exceptions=True),
                            timeout=5.0
                        )
                    except asyncio.TimeoutError:
                        self.logger.error("Some monitoring tasks failed to cancel properly")

            # Force cleanup connections
            cleanup_stats = await self.force_cleanup_connections()

            # Dispose of the engine
            if hasattr(self.engine, 'dispose'):
                self.engine.dispose()

            self.logger.info(f"Connection manager shutdown completed: {cleanup_stats}")

        except Exception as e:
            self.logger.error(f"Error during connection manager shutdown: {e}")
            raise


# Global connection manager instance (lazy initialization)
_connection_manager: Optional[EnhancedConnectionManager] = None


def get_connection_manager(database_config: Optional[DatabaseConfig] = None) -> EnhancedConnectionManager:
    """Get the global connection manager instance (lazy initialization)."""
    global _connection_manager

    if _connection_manager is None:
        if database_config is None:
            database_config = DatabaseConfig.from_env()
        _connection_manager = EnhancedConnectionManager(database_config)

    return _connection_manager


def get_db_session():
    """Get database session using the global connection manager."""
    connection_manager = get_connection_manager()
    return connection_manager.get_session()


@asynccontextmanager
async def get_db_session_with_retry(max_retries: int = 3, retry_delay: float = 1.0):
    """Get database session with retry logic using the global connection manager."""
    connection_manager = get_connection_manager()
    async with connection_manager.get_session_with_retry(max_retries, retry_delay) as session:
        yield session
