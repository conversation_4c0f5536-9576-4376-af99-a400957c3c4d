"""
Enhanced Dashboard Customization Models for Phase 2.4 implementation.
Supports user-customizable dashboard sections, widgets, and advanced customization features.
Includes core Dashboard entity for multi-dashboard functionality.
"""

from sqlalchemy import Column, Integer, String, Text, JSON, Boolean, DateTime, ForeignKey, Float, UniqueConstraint
from sqlalchemy.orm import relationship
from ..database import Base # Use relative import
from ..utils.db_utils import get_utc_now # Changed to relative import
from typing import Dict, Any, List, Optional, Union, Literal
from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict
from enum import Enum
import yaml
import json
from datetime import datetime


class VisualizationType(str, Enum):
    """Supported visualization types for dashboard widgets."""
    CHART = "chart"
    TABLE = "table"
    KPI = "kpi"
    GAUGE = "gauge"
    HEATMAP = "heatmap"
    NETWORK = "network"
    TREE = "tree"
    MAP = "map"
    TEXT = "text"
    IMAGE = "image"


class Dashboard(Base):
    """Core dashboard entity for multi-dashboard functionality."""
    __tablename__ = "dashboards"

    id = Column(String(36), primary_key=True, index=True)  # UUID
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    business_profile_id = Column(String(36), ForeignKey("business_profiles.id"), nullable=True, index=True)  # Optional business profile scoping
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    is_default = Column(Boolean, default=False)
    is_public = Column(Boolean, default=False)
    layout_config = Column(JSON, default=lambda: {})
    theme_config = Column(JSON, default=lambda: {})
    refresh_interval = Column(Integer, default=300)  # seconds
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    user = relationship("User", back_populates="dashboards")
    business_profile = relationship("BusinessProfile", back_populates="dashboards")
    sections = relationship("DashboardSection", back_populates="dashboard", cascade="all, delete-orphan")
    data_sources = relationship("DataSource", secondary="dashboard_data_sources", back_populates="dashboards")
    data_source_assignments = relationship("DashboardDataSourceAssignment", back_populates="dashboard", cascade="all, delete-orphan")

    # Constraints - Only allow one default dashboard per user
    # Note: This constraint should be implemented at the application level
    # since PostgreSQL doesn't support partial unique constraints easily
    __table_args__ = ()


class DashboardSection(Base):
    """Database model for dashboard sections."""
    __tablename__ = "dashboard_sections"

    id = Column(String(36), primary_key=True, index=True)
    dashboard_id = Column(String(36), ForeignKey("dashboards.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    color = Column(String(7), nullable=True)  # Hex color
    icon = Column(String(50), nullable=True)
    layout_config = Column(JSON, nullable=True)
    customization = Column(JSON, nullable=True)
    data_source_id = Column(String(36), ForeignKey("data_sources.id"), nullable=True)
    position = Column(Integer, default=0)  # For ordering sections
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    dashboard = relationship("Dashboard", back_populates="sections")
    widgets = relationship("DashboardWidget", back_populates="section", cascade="all, delete-orphan")
    user = relationship("User", back_populates="dashboard_sections")
    data_source = relationship("DataSource")


class DashboardWidget(Base):
    """Database model for dashboard widgets."""
    __tablename__ = "dashboard_widgets"

    id = Column(String(36), primary_key=True, index=True)
    section_id = Column(String(36), ForeignKey("dashboard_sections.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    title = Column(String(255), nullable=False)
    widget_type = Column(String(50), nullable=False)  # chart, table, kpi, etc.
    data_config = Column(JSON, nullable=True)  # Data source and query config
    visualization_config = Column(JSON, nullable=True)  # Chart type, colors, etc.
    position_config = Column(JSON, nullable=False)  # x, y, w, h
    customization = Column(JSON, nullable=True)  # Colors, hover settings, etc.
    refresh_interval = Column(Integer, default=300)  # seconds
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    section = relationship("DashboardSection", back_populates="widgets")
    user = relationship("User", back_populates="dashboard_widgets")
    insights = relationship("WidgetInsight", back_populates="widget", cascade="all, delete-orphan")


class WidgetInsight(Base):
    """Database model for AI-generated widget insights."""
    __tablename__ = "widget_insights"

    id = Column(String(36), primary_key=True, index=True)
    widget_id = Column(String(36), ForeignKey("dashboard_widgets.id"), nullable=False, index=True)
    insight_type = Column(String(50), nullable=False)  # summary, trend, anomaly, recommendation
    content = Column(JSON, nullable=False)  # AI-generated insights
    confidence_score = Column(Float, nullable=True)
    generated_at = Column(DateTime(timezone=True), default=get_utc_now)
    expires_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    widget = relationship("DashboardWidget", back_populates="insights")


class DashboardDataSource(Base):
    """Association table for dashboard-data source relationships."""
    __tablename__ = "dashboard_data_sources"

    dashboard_id = Column(String(36), ForeignKey("dashboards.id"), primary_key=True)
    data_source_id = Column(String(36), ForeignKey("data_sources.id"), primary_key=True)
    alias = Column(String(255), nullable=True)
    created_at = Column(DateTime(timezone=True), default=get_utc_now)


class DashboardDataSourceAssignment(Base):
    """Dashboard data source assignment model."""
    __tablename__ = "dashboard_data_source_assignments"

    id = Column(String(36), primary_key=True, index=True)
    dashboard_id = Column(String(36), ForeignKey("dashboards.id"), nullable=False)
    system_data_source_id = Column(String(36), ForeignKey("data_sources.id"), nullable=False)
    alias = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), default=get_utc_now)
    updated_at = Column(DateTime(timezone=True), default=get_utc_now, onupdate=get_utc_now)

    # Relationships
    dashboard = relationship("Dashboard", back_populates="data_source_assignments")
    system_data_source = relationship("DataSource")


# Pydantic models for API

class DashboardDataSourceCreate(BaseModel):
    """Schema for creating a dashboard data source."""
    model_config = ConfigDict(extra='forbid', validate_assignment=True)

    name: str = Field(..., min_length=1, max_length=255, description="Data source name")
    type: Literal["file", "database", "api", "platform"] = Field(..., description="Data source type")
    description: Optional[str] = Field(None, max_length=500, description="Data source description")
    connection_config: Dict[str, Any] = Field(default_factory=dict, description="Connection configuration")
    refresh_interval: int = Field(300, ge=30, le=3600, description="Refresh interval in seconds")

class DashboardDataSourceUpdate(BaseModel):
    """Schema for updating a dashboard data source."""
    model_config = ConfigDict(extra='forbid', validate_assignment=True)

    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Data source name")
    description: Optional[str] = Field(None, max_length=500, description="Data source description")
    connection_config: Optional[Dict[str, Any]] = Field(None, description="Connection configuration")
    refresh_interval: Optional[int] = Field(None, ge=30, le=3600, description="Refresh interval in seconds")
    is_active: Optional[bool] = Field(None, description="Whether the data source is active")


class DashboardDataSourceAssignmentCreate(BaseModel):
    """Schema for assigning existing data sources to dashboards."""
    model_config = ConfigDict(extra='forbid', validate_assignment=True)

    system_data_source_id: str = Field(..., description="ID of the existing system data source")
    alias: Optional[str] = Field(None, max_length=255, description="Optional alias for this data source within the dashboard")
    is_active: bool = Field(True, description="Whether this assignment is active")


class DashboardDataSourceAssignmentResponse(BaseModel):
    """Response schema for dashboard data source assignments."""
    model_config = ConfigDict(from_attributes=True, extra='forbid')

    id: str
    dashboard_id: str
    system_data_source_id: str
    alias: Optional[str]
    is_active: bool
    created_at: str
    updated_at: str
    # Optional populated system data source info
    system_data_source: Optional[Dict[str, Any]] = None

class DashboardDataSourceResponse(BaseModel):
    """Response schema for dashboard data sources."""
    model_config = ConfigDict(from_attributes=True, extra='forbid')

    id: str
    name: str
    type: str
    description: Optional[str]
    connection_config: Dict[str, Any]
    is_active: bool
    refresh_interval: int
    last_updated: Optional[str]
    record_count: Optional[int]

class DashboardCreate(BaseModel):
    """Schema for creating a dashboard."""
    model_config = ConfigDict(extra='forbid', validate_assignment=True)

    name: str = Field(..., min_length=1, max_length=255, description="Dashboard name")
    description: Optional[str] = Field(None, max_length=1000, description="Dashboard description")
    is_default: bool = Field(False, description="Whether this is the default dashboard")
    is_public: bool = Field(False, description="Whether this dashboard is public")
    layout_config: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Layout configuration")
    theme_config: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Theme configuration")
    refresh_interval: int = Field(300, ge=30, le=3600, description="Refresh interval in seconds")
    tags: Optional[List[str]] = Field(None, max_length=10, description="Dashboard tags")
    data_source_assignments: Optional[List[DashboardDataSourceAssignmentCreate]] = Field(None, description="Dashboard data source assignments")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if not v.strip():
            raise ValueError('Dashboard name cannot be empty or whitespace only')
        return v.strip()

    @field_validator('tags')
    @classmethod
    def validate_tags(cls, v):
        if v is not None:
            # Remove duplicates and empty tags
            v = list(set(tag.strip() for tag in v if tag.strip()))
            if len(v) > 10:
                raise ValueError('Maximum 10 tags allowed')
        return v

    @classmethod
    def from_yaml(cls, yaml_str: str) -> 'DashboardCreate':
        """Create dashboard from YAML configuration."""
        try:
            data = yaml.safe_load(yaml_str)
            return cls(**data)
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML: {e}")

    def to_yaml(self) -> str:
        """Convert dashboard to YAML configuration."""
        return yaml.dump(self.model_dump(), default_flow_style=False, sort_keys=True)


class DashboardUpdate(BaseModel):
    """Schema for updating a dashboard."""
    model_config = ConfigDict(extra='forbid', validate_assignment=True)

    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Dashboard name")
    description: Optional[str] = Field(None, max_length=1000, description="Dashboard description")
    is_default: Optional[bool] = Field(None, description="Whether this is the default dashboard")
    is_public: Optional[bool] = Field(None, description="Whether this dashboard is public")
    layout_config: Optional[Dict[str, Any]] = Field(None, description="Layout configuration")
    theme_config: Optional[Dict[str, Any]] = Field(None, description="Theme configuration")
    refresh_interval: Optional[int] = Field(None, ge=30, le=3600, description="Refresh interval in seconds")
    tags: Optional[List[str]] = Field(None, max_length=10, description="Dashboard tags")
    data_sources: Optional[List[DashboardDataSourceUpdate]] = Field(None, description="Dashboard data sources")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if v is not None and not v.strip():
            raise ValueError('Dashboard name cannot be empty or whitespace only')
        return v.strip() if v else v

    @field_validator('tags')
    @classmethod
    def validate_tags(cls, v):
        if v is not None:
            # Remove duplicates and empty tags
            v = list(set(tag.strip() for tag in v if tag.strip()))
            if len(v) > 10:
                raise ValueError('Maximum 10 tags allowed')
        return v


class DashboardResponse(BaseModel):
    """Response schema for dashboards."""
    model_config = ConfigDict(from_attributes=True, extra='forbid')

    id: str
    name: str
    description: Optional[str]
    is_default: bool
    is_public: bool
    layout_config: Dict[str, Any]
    theme_config: Dict[str, Any]
    refresh_interval: int
    created_at: str
    updated_at: str
    section_count: int = 0
    widget_count: int = 0
    tags: Optional[List[str]] = None
    data_sources: Optional[List[DashboardDataSourceResponse]] = None

    # Computed fields for better API responses
    @property
    def is_empty(self) -> bool:
        """Check if dashboard has no content."""
        return self.section_count == 0 and self.widget_count == 0

    @property
    def complexity_score(self) -> int:
        """Calculate dashboard complexity score."""
        return (self.section_count * 2) + self.widget_count


class DashboardError(BaseModel):
    """Error response schema for dashboard operations."""
    code: str
    message: str
    details: Optional[Dict[str, Any]] = None


class PositionConfig(BaseModel):
    """Widget position configuration."""
    x: int = Field(..., ge=0, description="X coordinate in grid")
    y: int = Field(..., ge=0, description="Y coordinate in grid")
    w: int = Field(..., ge=1, le=12, description="Width in grid units")
    h: int = Field(..., ge=1, le=12, description="Height in grid units")


class LayoutConfig(BaseModel):
    """Section layout configuration."""
    model_config = ConfigDict(extra='forbid', validate_assignment=True)

    columns: int = Field(12, ge=1, le=24, description="Number of grid columns")
    rows: int = Field(12, ge=1, le=24, description="Number of grid rows")
    grid_gap: int = Field(16, ge=0, le=50, description="Gap between grid items in pixels")
    margin: List[int] = Field([10, 10], min_length=2, max_length=2, description="Layout margin [x, y]")
    container_padding: List[int] = Field([10, 10], min_length=2, max_length=2, description="Container padding [x, y]")
    auto_size: bool = Field(True, description="Enable automatic sizing")

    @field_validator('margin', 'container_padding')
    @classmethod
    def validate_spacing(cls, v):
        if len(v) != 2:
            raise ValueError('Spacing must have exactly 2 values [x, y]')
        if any(val < 0 or val > 100 for val in v):
            raise ValueError('Spacing values must be between 0 and 100')
        return v


class ThemeConfig(BaseModel):
    """Dashboard theme configuration."""
    model_config = ConfigDict(extra='forbid', validate_assignment=True)

    primary_color: str = Field("#3B82F6", pattern=r"^#[0-9A-Fa-f]{6}$", description="Primary theme color")
    secondary_color: str = Field("#10B981", pattern=r"^#[0-9A-Fa-f]{6}$", description="Secondary theme color")
    background_color: str = Field("#F9FAFB", pattern=r"^#[0-9A-Fa-f]{6}$", description="Background color")
    text_color: str = Field("#1F2937", pattern=r"^#[0-9A-Fa-f]{6}$", description="Text color")
    border_color: str = Field("#E5E7EB", pattern=r"^#[0-9A-Fa-f]{6}$", description="Border color")
    accent_color: str = Field("#F59E0B", pattern=r"^#[0-9A-Fa-f]{6}$", description="Accent color")
    success_color: str = Field("#10B981", pattern=r"^#[0-9A-Fa-f]{6}$", description="Success color")
    warning_color: str = Field("#F59E0B", pattern=r"^#[0-9A-Fa-f]{6}$", description="Warning color")
    error_color: str = Field("#EF4444", pattern=r"^#[0-9A-Fa-f]{6}$", description="Error color")
    font_family: str = Field("Inter, system-ui, sans-serif", description="Font family")
    font_size_base: int = Field(14, ge=10, le=20, description="Base font size in pixels")
    border_radius: int = Field(8, ge=0, le=20, description="Border radius in pixels")
    shadow_level: Literal["none", "sm", "md", "lg", "xl"] = Field("md", description="Shadow level")

    @classmethod
    def from_yaml(cls, yaml_str: str) -> 'ThemeConfig':
        """Create theme config from YAML string."""
        try:
            data = yaml.safe_load(yaml_str)
            return cls(**data)
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML: {e}")

    def to_yaml(self) -> str:
        """Convert theme config to YAML string."""
        return yaml.dump(self.model_dump(), default_flow_style=False, sort_keys=True)


class SectionCustomization(BaseModel):
    """Section customization options."""
    model_config = ConfigDict(extra='forbid', validate_assignment=True)

    background_color: Optional[str] = Field(None, pattern=r"^#[0-9A-Fa-f]{6}$", description="Background color hex")
    border_color: Optional[str] = Field(None, pattern=r"^#[0-9A-Fa-f]{6}$", description="Border color hex")
    border_width: Optional[int] = Field(1, ge=0, le=5, description="Border width in pixels")
    border_style: Optional[Literal["solid", "dashed", "dotted", "none"]] = Field("solid", description="Border style")
    padding: Optional[List[int]] = Field([16, 16], min_length=2, max_length=4, description="Section padding")
    margin: Optional[List[int]] = Field([8, 8], min_length=2, max_length=4, description="Section margin")
    header_style: Optional[Dict[str, Any]] = Field(None, description="Header styling options")
    shadow: Optional[bool] = Field(True, description="Enable section shadow")

    @field_validator('padding', 'margin')
    @classmethod
    def validate_spacing(cls, v):
        if len(v) not in [2, 4]:
            raise ValueError('Spacing must have 2 or 4 values')
        if any(val < 0 or val > 100 for val in v):
            raise ValueError('Spacing values must be between 0 and 100')
        return v


class WidgetCustomization(BaseModel):
    """Widget customization options."""
    model_config = ConfigDict(extra='forbid', validate_assignment=True)

    colors: Optional[List[str]] = Field(None, min_length=1, max_length=20, description="Custom color palette")
    chart_type: Optional[Literal["line", "bar", "area", "pie", "donut", "scatter", "bubble"]] = Field(None, description="Chart type override")
    axis_config: Optional[Dict[str, Any]] = Field(None, description="Axis configuration")
    hover_insights: bool = Field(True, description="Enable hover insights")
    animation_enabled: bool = Field(True, description="Enable animations")
    legend_position: Literal["top", "bottom", "left", "right", "none"] = Field("bottom", description="Legend position")
    grid_lines: bool = Field(True, description="Show grid lines")
    data_labels: bool = Field(False, description="Show data labels")
    responsive: bool = Field(True, description="Enable responsive design")
    background_color: Optional[str] = Field(None, pattern=r"^#[0-9A-Fa-f]{6}$", description="Widget background color")
    border_radius: int = Field(8, ge=0, le=20, description="Border radius in pixels")

    @field_validator('colors')
    @classmethod
    def validate_colors(cls, v):
        if v is not None:
            for color in v:
                if not color.startswith('#') or len(color) != 7:
                    raise ValueError('Colors must be valid hex codes (e.g., #FF0000)')
        return v


class DataSourceConfig(BaseModel):
    """Legacy data source configuration for widgets (deprecated - use WidgetDataSourceConfig)."""
    model_config = ConfigDict(extra='forbid', validate_assignment=True)

    id: str = Field(..., min_length=1, description="Data source ID")
    name: str = Field(..., min_length=1, max_length=255, description="Data source name")
    type: Literal["file", "database", "api", "mcp", "stream"] = Field(..., description="Data source type")
    connection_string: Optional[str] = Field(None, description="Connection string for databases/APIs")
    query: Optional[str] = Field(None, description="Query or filter expression")
    filters: Optional[Dict[str, Any]] = Field(None, description="Data filters")
    refresh_rate: int = Field(300, ge=30, le=3600, description="Data refresh rate in seconds")
    cache_enabled: bool = Field(True, description="Enable data caching")
    timeout: int = Field(30, ge=5, le=300, description="Request timeout in seconds")

    @field_validator('connection_string')
    @classmethod
    def validate_connection_string(cls, v, info):
        if info.data.get('type') in ['database', 'api'] and not v:
            raise ValueError('Connection string is required for database and API data sources')
        return v

class WidgetDataSourceConfig(BaseModel):
    """Widget data source configuration that references dashboard-level data sources."""
    model_config = ConfigDict(extra='forbid', validate_assignment=True)

    dashboard_data_source_id: str = Field(..., min_length=1, description="Dashboard data source ID")
    query: Optional[str] = Field(None, description="Query or filter expression")
    filters: Optional[Dict[str, Any]] = Field(None, description="Data filters")
    aggregation: Optional[str] = Field(None, description="Aggregation method")
    group_by: Optional[List[str]] = Field(None, description="Group by fields")
    sort_by: Optional[str] = Field(None, description="Sort by field")
    limit: int = Field(100, ge=1, le=10000, description="Record limit")


class SectionCreate(BaseModel):
    """Schema for creating a dashboard section."""
    model_config = ConfigDict(extra='forbid', validate_assignment=True)

    dashboard_id: str = Field(..., min_length=1, description="Parent dashboard ID")
    name: str = Field(..., min_length=1, max_length=255, description="Section name")
    description: Optional[str] = Field(None, max_length=500, description="Section description")
    color: Optional[str] = Field("#3B82F6", pattern=r"^#[0-9A-Fa-f]{6}$", description="Section color")
    icon: Optional[str] = Field("Grid", max_length=50, description="Section icon")
    layout_config: Optional[Dict[str, Any]] = Field(None, description="Layout configuration")
    customization: Optional[Dict[str, Any]] = Field(None, description="Customization options")
    data_source_id: Optional[str] = Field(None, description="Associated data source")
    template: Optional[Literal["analytics_overview", "performance_monitoring", "business_metrics", "custom"]] = Field(None, description="Template to apply")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if not v.strip():
            raise ValueError('Section name cannot be empty or whitespace only')
        return v.strip()

    @field_validator('icon')
    @classmethod
    def validate_icon(cls, v):
        # List of allowed icons (you can expand this)
        allowed_icons = [
            "Grid", "BarChart3", "Table", "Gauge", "Network", "TreePine",
            "Map", "Type", "Image", "Activity", "TrendingUp", "Users",
            "Database", "Settings", "Eye", "Heart", "Star"
        ]
        if v and v not in allowed_icons:
            raise ValueError(f'Icon must be one of: {", ".join(allowed_icons)}')
        return v


class SectionUpdate(BaseModel):
    """Schema for updating a dashboard section."""
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Section name")
    description: Optional[str] = Field(None, description="Section description")
    color: Optional[str] = Field(None, pattern=r"^#[0-9A-Fa-f]{6}$", description="Section color")
    icon: Optional[str] = Field(None, max_length=50, description="Section icon")
    layout_config: Optional[LayoutConfig] = Field(None, description="Layout configuration")
    customization: Optional[SectionCustomization] = Field(None, description="Customization options")
    data_source_id: Optional[str] = Field(None, description="Associated data source")
    position: Optional[int] = Field(None, ge=0, description="Section position")
    is_active: Optional[bool] = Field(None, description="Section active status")


class WidgetCreate(BaseModel):
    """Schema for creating a dashboard widget."""
    model_config = ConfigDict(extra='forbid', validate_assignment=True)

    section_id: str = Field(..., min_length=1, description="Parent section ID")
    title: str = Field(..., min_length=1, max_length=255, description="Widget title")
    widget_type: VisualizationType = Field(..., description="Widget type")
    data_config: Optional[Dict[str, Any]] = Field(None, description="Data configuration")
    visualization_config: Optional[Dict[str, Any]] = Field(None, description="Visualization configuration")
    position_config: Dict[str, Any] = Field(..., description="Position configuration")
    customization: Optional[Dict[str, Any]] = Field(None, description="Customization options")
    refresh_interval: int = Field(300, ge=30, le=3600, description="Refresh interval in seconds")

    @field_validator('title')
    @classmethod
    def validate_title(cls, v):
        if not v.strip():
            raise ValueError('Widget title cannot be empty or whitespace only')
        return v.strip()

    @field_validator('position_config')
    @classmethod
    def validate_position_config(cls, v):
        required_fields = ['x', 'y', 'w', 'h']
        for field in required_fields:
            if field not in v:
                raise ValueError(f'Position config must include {field}')
            if not isinstance(v[field], int) or v[field] < 0:
                raise ValueError(f'Position {field} must be a non-negative integer')

        # Validate grid constraints
        if v['w'] < 1 or v['w'] > 12:
            raise ValueError('Width (w) must be between 1 and 12')
        if v['h'] < 1 or v['h'] > 12:
            raise ValueError('Height (h) must be between 1 and 12')

        return v


class WidgetUpdate(BaseModel):
    """Schema for updating a dashboard widget."""
    title: Optional[str] = Field(None, min_length=1, max_length=255, description="Widget title")
    widget_type: Optional[VisualizationType] = Field(None, description="Widget visualization type")
    data_config: Optional[WidgetDataSourceConfig] = Field(None, description="Data configuration")
    visualization_config: Optional[Dict[str, Any]] = Field(None, description="Visualization configuration")
    position_config: Optional[PositionConfig] = Field(None, description="Position configuration")
    customization: Optional[WidgetCustomization] = Field(None, description="Customization options")
    refresh_interval: Optional[int] = Field(None, ge=30, le=3600, description="Refresh interval in seconds")
    is_active: Optional[bool] = Field(None, description="Widget active status")


class WidgetMoveRequest(BaseModel):
    """Schema for moving a widget between sections."""
    widget_id: str = Field(..., description="Widget ID to move")
    target_section_id: str = Field(..., description="Target section ID")
    position_config: PositionConfig = Field(..., description="New position configuration")


class SectionResponse(BaseModel):
    """Response schema for dashboard sections."""
    id: str
    name: str
    description: Optional[str]
    color: Optional[str]
    icon: Optional[str]
    layout_config: Optional[Dict[str, Any]]
    customization: Optional[Dict[str, Any]]
    data_source_id: Optional[str]
    position: int
    is_active: bool
    created_at: str
    updated_at: str
    widget_count: int


class WidgetResponse(BaseModel):
    """Response schema for dashboard widgets."""
    id: str
    section_id: str
    title: str
    widget_type: str
    data_config: Optional[Dict[str, Any]]
    visualization_config: Optional[Dict[str, Any]]
    position_config: Dict[str, Any]
    customization: Optional[Dict[str, Any]]
    refresh_interval: int
    is_active: bool
    created_at: str
    updated_at: str


class WidgetInsightResponse(BaseModel):
    """Response schema for widget insights."""
    summary: Dict[str, Any]
    ai_insights: Optional[Dict[str, Any]]
    trends: Optional[Dict[str, Any]]
    confidence: float


class DashboardTemplate(BaseModel):
    """Dashboard template configuration with YAML support."""
    model_config = ConfigDict(extra='forbid', validate_assignment=True)

    name: str = Field(..., min_length=1, max_length=255, description="Template name")
    description: str = Field(..., min_length=1, max_length=1000, description="Template description")
    category: Literal["analytics", "business", "monitoring", "custom"] = Field(..., description="Template category")
    dashboard_config: DashboardCreate = Field(..., description="Dashboard configuration")
    sections: List[Dict[str, Any]] = Field(..., min_length=1, description="Section configurations")
    widgets: List[Dict[str, Any]] = Field(default_factory=list, description="Widget configurations")
    version: str = Field("1.0.0", pattern=r"^\d+\.\d+\.\d+$", description="Template version")
    author: Optional[str] = Field(None, description="Template author")
    tags: List[str] = Field(default_factory=list, max_length=10, description="Template tags")

    @field_validator('sections')
    @classmethod
    def validate_sections(cls, v):
        if not v:
            raise ValueError('Template must have at least one section')

        for i, section in enumerate(v):
            if 'name' not in section:
                raise ValueError(f'Section {i} must have a name')
            if not isinstance(section['name'], str) or not section['name'].strip():
                raise ValueError(f'Section {i} name must be a non-empty string')

        return v

    @classmethod
    def from_yaml_file(cls, file_path: str) -> 'DashboardTemplate':
        """Load dashboard template from YAML file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            return cls(**data)
        except FileNotFoundError:
            raise ValueError(f"Template file not found: {file_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML in template file: {e}")

    @classmethod
    def from_yaml(cls, yaml_str: str) -> 'DashboardTemplate':
        """Create dashboard template from YAML string."""
        try:
            data = yaml.safe_load(yaml_str)
            return cls(**data)
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML: {e}")

    def to_yaml(self) -> str:
        """Convert template to YAML string."""
        return yaml.dump(self.model_dump(), default_flow_style=False, sort_keys=True)

    def save_to_file(self, file_path: str) -> None:
        """Save template to YAML file."""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(self.to_yaml())


class DashboardLayoutResponse(BaseModel):
    """Response schema for complete dashboard layout."""
    model_config = ConfigDict(from_attributes=True, extra='forbid')

    dashboard: DashboardResponse
    sections: List[SectionResponse]
    widgets: List[WidgetResponse]
    total_sections: int
    total_widgets: int
    last_updated: str

    @property
    def is_empty(self) -> bool:
        """Check if layout has no content."""
        return self.total_sections == 0 and self.total_widgets == 0

    @property
    def widget_distribution(self) -> Dict[str, int]:
        """Get widget count by type."""
        distribution = {}
        for widget in self.widgets:
            widget_type = widget.widget_type
            distribution[widget_type] = distribution.get(widget_type, 0) + 1
        return distribution


class DashboardConfigValidator:
    """Utility class for validating dashboard configurations."""

    @staticmethod
    def validate_layout_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and normalize layout configuration."""
        try:
            layout = LayoutConfig(**config)
            return layout.model_dump()
        except Exception as e:
            raise ValueError(f"Invalid layout configuration: {e}")

    @staticmethod
    def validate_theme_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and normalize theme configuration."""
        try:
            theme = ThemeConfig(**config)
            return theme.model_dump()
        except Exception as e:
            raise ValueError(f"Invalid theme configuration: {e}")

    @staticmethod
    def validate_widget_position(position: Dict[str, Any], grid_columns: int = 12) -> Dict[str, Any]:
        """Validate widget position within grid constraints."""
        try:
            pos = PositionConfig(**position)

            # Check if widget fits within grid
            if pos.x + pos.w > grid_columns:
                raise ValueError(f"Widget extends beyond grid width (x: {pos.x}, w: {pos.w}, grid: {grid_columns})")

            return pos.model_dump()
        except Exception as e:
            raise ValueError(f"Invalid widget position: {e}")

    @staticmethod
    def validate_dashboard_yaml(yaml_str: str) -> Dict[str, Any]:
        """Validate complete dashboard YAML configuration."""
        try:
            data = yaml.safe_load(yaml_str)

            # Validate dashboard structure
            if 'dashboard' not in data:
                raise ValueError("YAML must contain 'dashboard' section")

            # Validate dashboard config
            dashboard_config = DashboardCreate(**data['dashboard'])

            # Validate sections if present
            if 'sections' in data:
                for i, section_data in enumerate(data['sections']):
                    try:
                        # Add dashboard_id for validation (will be replaced during creation)
                        section_data['dashboard_id'] = 'temp-id'
                        SectionCreate(**section_data)
                    except Exception as e:
                        raise ValueError(f"Invalid section {i}: {e}")

            # Validate widgets if present
            if 'widgets' in data:
                for i, widget_data in enumerate(data['widgets']):
                    try:
                        # Add section_id for validation (will be replaced during creation)
                        widget_data['section_id'] = 'temp-id'
                        WidgetCreate(**widget_data)
                    except Exception as e:
                        raise ValueError(f"Invalid widget {i}: {e}")

            return data

        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML syntax: {e}")
        except Exception as e:
            raise ValueError(f"Configuration validation failed: {e}")

    @staticmethod
    def get_default_dashboard_config() -> Dict[str, Any]:
        """Get default dashboard configuration."""
        return {
            "dashboard": {
                "name": "New Dashboard",
                "description": "A new dashboard for data visualization",
                "is_default": False,
                "is_public": False,
                "layout_config": {
                    "columns": 12,
                    "rows": 12,
                    "grid_gap": 16,
                    "margin": [10, 10],
                    "container_padding": [10, 10],
                    "auto_size": True
                },
                "theme_config": {
                    "primary_color": "#3B82F6",
                    "secondary_color": "#10B981",
                    "background_color": "#F9FAFB",
                    "text_color": "#1F2937",
                    "border_color": "#E5E7EB",
                    "accent_color": "#F59E0B",
                    "success_color": "#10B981",
                    "warning_color": "#F59E0B",
                    "error_color": "#EF4444",
                    "font_family": "Inter, system-ui, sans-serif",
                    "font_size_base": 14,
                    "border_radius": 8,
                    "shadow_level": "md"
                },
                "refresh_interval": 300,
                "tags": []
            },
            "sections": [],
            "widgets": []
        }
