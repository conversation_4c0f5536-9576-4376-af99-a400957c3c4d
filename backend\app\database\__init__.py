"""
Database package for enhanced connection management and monitoring.

This package provides:
- Enhanced connection manager with leak detection
- Connection pool monitoring
- Retry logic and timeout handling
- Memory-efficient database operations
"""

# Import Base and other essentials from the main database module
from ..database import Base, engine, SessionLocal, get_utc_now

from .connection_manager import (
    EnhancedConnectionManager,
    ConnectionInfo,
    ConnectionPoolStats,
    get_connection_manager,
    get_db_session,
    get_db_session_with_retry
)

__all__ = [
    'Base',
    'engine',
    'SessionLocal',
    'get_utc_now',
    'EnhancedConnectionManager',
    'ConnectionInfo',
    'ConnectionPoolStats',
    'get_connection_manager',
    'get_db_session',
    'get_db_session_with_retry'
]
